import customtkinter as ctk
import threading
import time
from typing import Optional, Dict, Any
from database import DatabaseManager
from time_service import TimeService
import datetime

class EmployeeInterface(ctk.CTk):
    def __init__(self, db_manager: DatabaseManager, time_service: TimeService):
        super().__init__()
        
        self.db_manager = db_manager
        self.time_service = time_service
        self.selected_employee = None
        self.pin_attempts = {}  # Track PIN attempts per employee
        
        self.setup_window()
        self.create_widgets()
        self.start_clock()
        self.refresh_employee_list()
        
        # Perform initial time audit if online
        self.perform_time_audit()
    
    def setup_window(self):
        """Setup the main window"""
        self.title("PolicyTime - Employee Interface")
        self.geometry("800x600")
        self.resizable(True, True)
        
        # Configure grid
        self.grid_columnconfigure(0, weight=1)
        self.grid_columnconfigure(1, weight=1)
        self.grid_rowconfigure(0, weight=1)
    
    def create_widgets(self):
        """Create the main interface widgets"""
        # Left Panel - Employee List
        self.left_frame = ctk.CTkFrame(self)
        self.left_frame.grid(row=0, column=0, padx=10, pady=10, sticky="nsew")
        self.left_frame.grid_columnconfigure(0, weight=1)
        self.left_frame.grid_rowconfigure(1, weight=1)
        
        # Employee list header
        self.employee_header = ctk.CTkLabel(
            self.left_frame, 
            text="Active Employees", 
            font=ctk.CTkFont(size=18, weight="bold")
        )
        self.employee_header.grid(row=0, column=0, padx=10, pady=(10, 5), sticky="w")
        
        # Employee list
        self.employee_listbox = ctk.CTkScrollableFrame(self.left_frame)
        self.employee_listbox.grid(row=1, column=0, padx=10, pady=(0, 10), sticky="nsew")
        
        # Right Panel - Status and Info
        self.right_frame = ctk.CTkFrame(self)
        self.right_frame.grid(row=0, column=1, padx=10, pady=10, sticky="nsew")
        self.right_frame.grid_columnconfigure(0, weight=1)
        self.right_frame.grid_rowconfigure(3, weight=1)
        
        # Current time
        self.time_label = ctk.CTkLabel(
            self.right_frame,
            text="",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        self.time_label.grid(row=0, column=0, padx=20, pady=(20, 10), sticky="ew")
        
        # Network status
        self.network_status = ctk.CTkLabel(
            self.right_frame,
            text="",
            font=ctk.CTkFont(size=16)
        )
        self.network_status.grid(row=1, column=0, padx=20, pady=(0, 20), sticky="ew")
        
        # Status message
        self.status_message = ctk.CTkLabel(
            self.right_frame,
            text="Welcome to PolicyTime!",
            font=ctk.CTkFont(size=14),
            wraplength=350
        )
        self.status_message.grid(row=2, column=0, padx=20, pady=(0, 20), sticky="ew")
        
        # Admin button
        self.admin_button = ctk.CTkButton(
            self.right_frame,
            text="Admin Panel",
            command=self.open_admin_panel,
            height=40
        )
        self.admin_button.grid(row=4, column=0, padx=20, pady=(0, 20), sticky="ew")
    
    def start_clock(self):
        """Start the clock update thread"""
        def update_clock():
            while True:
                try:
                    # Use local time for display
                    current_time = datetime.datetime.now()
                    
                    # Update time display
                    time_str = current_time.strftime("%I:%M:%S %p")
                    date_str = current_time.strftime("%A, %B %d, %Y")
                    
                    self.time_label.configure(text=f"{time_str}\n{date_str}")
                    
                    # Show offline status by default (NTP only checked during sign-in/out)
                    self.network_status.configure(
                        text="🟡 Local Time Mode",
                        text_color="orange"
                    )
                    
                    time.sleep(1)
                except Exception as e:
                    print(f"Clock update error: {e}")
                    time.sleep(1)
        
        clock_thread = threading.Thread(target=update_clock, daemon=True)
        clock_thread.start()
    
    def refresh_employee_list(self):
        """Refresh the employee list"""
        # Clear existing list
        for widget in self.employee_listbox.winfo_children():
            widget.destroy()
        
        # Get active employees
        employees = self.db_manager.get_active_employees()
        
        if not employees:
            # Show message if no employees
            no_emp_label = ctk.CTkLabel(
                self.employee_listbox,
                text="No active employees found.\nPlease contact administrator.",
                font=ctk.CTkFont(size=14),
                text_color="gray"
            )
            no_emp_label.pack(pady=20)
            return
        
        # Create employee buttons
        for employee in employees:
            emp_frame = ctk.CTkFrame(self.employee_listbox)
            emp_frame.pack(fill="x", padx=5, pady=2)
            
            # Employee name and PIN indicator
            pin_indicator = "🔒" if employee['has_pin'] else "🔓"
            emp_button = ctk.CTkButton(
                emp_frame,
                text=f"{pin_indicator} {employee['name']}",
                command=lambda e=employee: self.select_employee(e),
                height=40,
                fg_color="transparent",
                text_color=("gray10", "gray90"),
                hover_color=("gray70", "gray30")
            )
            emp_button.pack(fill="x", padx=5, pady=5)
    
    def select_employee(self, employee: Dict[str, Any]):
        """Handle employee selection"""
        self.selected_employee = employee
        
        # Check if PIN is required
        pin_required = self.db_manager.get_config('pin_required') == 'true'
        
        if pin_required and employee['has_pin']:
            self.show_pin_dialog(employee)
        else:
            self.show_confirmation_dialog(employee)
    
    def show_pin_dialog(self, employee: Dict[str, Any]):
        """Show PIN entry dialog"""
        pin_dialog = ctk.CTkToplevel(self)
        pin_dialog.title("Enter PIN")
        pin_dialog.geometry("300x200")
        pin_dialog.resizable(False, False)
        pin_dialog.transient(self)
        pin_dialog.grab_set()
        
        # Center the dialog
        pin_dialog.update_idletasks()
        x = (pin_dialog.winfo_screenwidth() // 2) - (300 // 2)
        y = (pin_dialog.winfo_screenheight() // 2) - (200 // 2)
        pin_dialog.geometry(f"300x200+{x}+{y}")
        
        # PIN entry
        pin_label = ctk.CTkLabel(
            pin_dialog,
            text=f"Enter PIN for {employee['name']}:",
            font=ctk.CTkFont(size=16)
        )
        pin_label.pack(pady=(20, 10))
        
        pin_entry = ctk.CTkEntry(
            pin_dialog,
            placeholder_text="Enter 4-digit PIN",
            show="*",
            font=ctk.CTkFont(size=16)
        )
        pin_entry.pack(pady=(0, 20), padx=20, fill="x")
        pin_entry.focus()
        
        # Error message
        error_label = ctk.CTkLabel(
            pin_dialog,
            text="",
            text_color="red",
            font=ctk.CTkFont(size=12)
        )
        error_label.pack(pady=(0, 10))
        
        def validate_pin():
            pin = pin_entry.get()
            
            if not pin.isdigit() or len(pin) != 4:
                error_label.configure(text="PIN must be 4 digits")
                return
            
            # Check PIN attempts
            emp_id = employee['id']
            if emp_id not in self.pin_attempts:
                self.pin_attempts[emp_id] = 0
            
            if self.pin_attempts[emp_id] >= 3:
                error_label.configure(text="Too many attempts. Try again later.")
                return
            
            if self.db_manager.verify_employee_pin(emp_id, pin):
                pin_dialog.destroy()
                self.pin_attempts[emp_id] = 0  # Reset attempts
                self.show_confirmation_dialog(employee)
            else:
                self.pin_attempts[emp_id] += 1
                attempts_left = 3 - self.pin_attempts[emp_id]
                error_label.configure(text=f"Invalid PIN. {attempts_left} attempts left.")
                pin_entry.delete(0, 'end')
        
        # Buttons
        button_frame = ctk.CTkFrame(pin_dialog)
        button_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        cancel_button = ctk.CTkButton(
            button_frame,
            text="Cancel",
            command=pin_dialog.destroy,
            fg_color="gray"
        )
        cancel_button.pack(side="left", padx=(0, 10), expand=True)
        
        submit_button = ctk.CTkButton(
            button_frame,
            text="Submit",
            command=validate_pin
        )
        submit_button.pack(side="right", expand=True)
        
        # Bind Enter key
        pin_entry.bind('<Return>', lambda e: validate_pin())
    
    def show_confirmation_dialog(self, employee: Dict[str, Any]):
        """Show sign-in/out confirmation dialog"""
        # Get last action to determine next action
        last_action = self.db_manager.get_employee_last_action(employee['id'])
        
        if last_action and last_action['action'] == 'SIGN_IN':
            next_action = 'SIGN_OUT'
            action_text = 'Sign Out'
        else:
            next_action = 'SIGN_IN'
            action_text = 'Sign In'
        
        # Check for forgotten sign-out and resolve if needed
        if next_action == 'SIGN_IN':
            self.time_service.check_and_resolve_forgotten_signout(employee['id'])
        
        # Show confirmation
        confirm_dialog = ctk.CTkToplevel(self)
        confirm_dialog.title("Confirm Action")
        confirm_dialog.geometry("400x200")
        confirm_dialog.resizable(False, False)
        confirm_dialog.transient(self)
        confirm_dialog.grab_set()
        
        # Center the dialog
        confirm_dialog.update_idletasks()
        x = (confirm_dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (confirm_dialog.winfo_screenheight() // 2) - (200 // 2)
        confirm_dialog.geometry(f"400x200+{x}+{y}")
        
        # Confirmation message
        confirm_label = ctk.CTkLabel(
            confirm_dialog,
            text=f"{action_text} as {employee['name']}?",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        confirm_label.pack(pady=(30, 20))
        
        def confirm_action():
            success = self.perform_sign_action(employee['id'], next_action)
            if success:
                self.status_message.configure(
                    text=f"Successfully signed {next_action.lower().replace('_', ' ')} as {employee['name']}",
                    text_color="green"
                )
            else:
                self.status_message.configure(
                    text=f"Error performing {action_text.lower()}",
                    text_color="red"
                )
            confirm_dialog.destroy()
        
        # Buttons
        button_frame = ctk.CTkFrame(confirm_dialog)
        button_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        cancel_button = ctk.CTkButton(
            button_frame,
            text="Cancel",
            command=confirm_dialog.destroy,
            fg_color="gray"
        )
        cancel_button.pack(side="left", padx=(0, 10), expand=True)
        
        confirm_button = ctk.CTkButton(
            button_frame,
            text=action_text,
            command=confirm_action
        )
        confirm_button.pack(side="right", expand=True)
    
    def perform_sign_action(self, employee_id: int, action: str) -> bool:
        """Perform the actual sign-in/out action"""
        try:
            # Get current time with NTP verification (only during sign-in/out)
            current_time, status, ntp_server, local_time = self.time_service.get_current_time()
            
            # Update network status based on NTP verification
            if status == 'VERIFIED':
                self.network_status.configure(
                    text="🟢 Online Mode (NTP Verified)",
                    text_color="green"
                )
            else:
                self.network_status.configure(
                    text="🔴 Offline Mode (Local Time)",
                    text_color="red"
                )
            
            # Add time log entry
            success = self.db_manager.add_time_log(
                employee_id=employee_id,
                action=action,
                timestamp=current_time,
                status=status,
                ntp_server=ntp_server,
                local_time=local_time
            )
            
            return success
            
        except Exception as e:
            print(f"Error performing sign action: {e}")
            return False
    
    def perform_time_audit(self):
        """Perform background time audit"""
        def audit_thread():
            try:
                # Only audit if we have internet connection
                current_time, status, _, _ = self.time_service.get_current_time()
                if status == 'VERIFIED':
                    audit_results = self.time_service.audit_offline_entries()
                    if audit_results['total_checked'] > 0:
                        print(f"Time audit completed: {audit_results}")
            except Exception as e:
                print(f"Time audit error: {e}")
        
        # Run audit in background thread
        audit_thread_obj = threading.Thread(target=audit_thread, daemon=True)
        audit_thread_obj.start()
    
    def open_admin_panel(self):
        """Open the administrator panel"""
        # Check if admin password is required
        admin_password_required = self.db_manager.get_config('admin_password_required') != 'false'
        
        if admin_password_required:
            from admin_auth import AdminAuthDialog
            
            # Show authentication dialog
            auth_dialog = AdminAuthDialog(self.db_manager, self)
            self.wait_window(auth_dialog)
            
            # Check if authentication was successful
            if not auth_dialog.is_authenticated():
                return  # Exit if authentication failed
        else:
            # Skip authentication if disabled
            pass
        
        # Open admin panel
        from admin_panel import AdminPanel
        admin_window = AdminPanel(self.db_manager, self.time_service, self.refresh_employee_list)
        admin_window.lift()
        admin_window.focus_force()
        admin_window.grab_set()
        admin_window.wait_window() 